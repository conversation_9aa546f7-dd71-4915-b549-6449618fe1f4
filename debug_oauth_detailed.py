#!/usr/bin/env python3
"""
Debug OAuth dengan detail logging
"""
import asyncio
import httpx
from supabase import create_client
import os
from dotenv import load_dotenv
import json

load_dotenv()

async def debug_oauth_step_by_step():
    """Debug OAuth step by step"""
    
    print("🔍 OAuth Detailed Debug")
    print("=" * 50)
    
    # Test 1: Supabase connection dan table
    print("\n1. 🗄️ Testing Supabase Connection")
    print("-" * 30)
    
    supabase_url = os.getenv("SUPABASE_URL")
    supabase_key = os.getenv("SUPABASE_ANON_KEY")
    
    try:
        supabase = create_client(supabase_url, supabase_key)
        
        # Test table access
        response = supabase.table('user_profiles').select('*').limit(1).execute()
        print("✅ Supabase table access working")
        print(f"📋 Table columns: {list(response.data[0].keys()) if response.data else 'No data'}")
        
    except Exception as e:
        print(f"❌ Supabase error: {e}")
        return
    
    # Test 2: Google OAuth token exchange simulation
    print("\n2. 🔐 Testing Google OAuth Token Exchange")
    print("-" * 30)
    
    client_id = os.getenv('GOOGLE_CLIENT_ID')
    client_secret = os.getenv('GOOGLE_CLIENT_SECRET')
    redirect_uri = os.getenv('GOOGLE_REDIRECT_URI')
    
    print(f"Client ID: {client_id}")
    print(f"Redirect URI: {redirect_uri}")
    
    # Test 3: User creation simulation
    print("\n3. 👤 Testing User Creation")
    print("-" * 30)
    
    # Simulate user data from Google
    mock_user_info = {
        'id': 'google-test-123',
        'email': '<EMAIL>',
        'name': 'Test User',
        'avatar': 'https://example.com/avatar.jpg',
        'provider': 'google'
    }
    
    try:
        # Test user creation
        import uuid
        user_id = str(uuid.uuid4())
        
        new_user_data = {
            'id': user_id,
            'email': mock_user_info['email'],
            'full_name': mock_user_info.get('name', ''),
            'avatar_url': mock_user_info.get('avatar', ''),
            'provider': mock_user_info['provider'],
            'provider_id': mock_user_info['id']
        }
        
        print(f"📝 Attempting to create user: {json.dumps(new_user_data, indent=2)}")
        
        response = supabase.table('user_profiles').insert(new_user_data).execute()
        
        if response.data:
            print("✅ User creation successful")
            created_user = response.data[0]
            print(f"👤 Created user: {json.dumps(created_user, indent=2)}")
            
            # Test JWT token generation
            print("\n4. 🔑 Testing JWT Token Generation")
            print("-" * 30)
            
            from jose import jwt
            from datetime import datetime, timedelta
            
            secret_key = os.getenv('SECRET_KEY')
            algorithm = os.getenv('ALGORITHM', 'HS256')
            
            # Create access token
            token_data = {
                "sub": str(created_user['id']),
                "email": created_user['email'],
                "provider": created_user.get('provider', 'oauth'),
                "exp": datetime.utcnow() + timedelta(minutes=30)
            }
            
            access_token = jwt.encode(token_data, secret_key, algorithm=algorithm)
            print("✅ JWT token generation successful")
            print(f"🔑 Token length: {len(access_token)}")
            
            # Verify token
            decoded = jwt.decode(access_token, secret_key, algorithms=[algorithm])
            print("✅ JWT token verification successful")
            print(f"📋 Token payload: {json.dumps(decoded, indent=2, default=str)}")
            
            # Clean up test user
            supabase.table('user_profiles').delete().eq('id', user_id).execute()
            print("✅ Test user cleaned up")
            
        else:
            print("❌ User creation failed - no data returned")
            print(f"Response: {response}")
            
    except Exception as e:
        print(f"❌ User creation error: {e}")
        print(f"Error type: {type(e)}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")
    
    # Test 4: Backend OAuth callback endpoint
    print("\n5. 🌐 Testing Backend OAuth Callback")
    print("-" * 30)
    
    try:
        async with httpx.AsyncClient() as client:
            # Test with invalid code to see error handling
            callback_url = "http://localhost:8001/api/v1/auth/oauth/callback/google"
            params = {
                'code': 'test-invalid-code',
                'state': 'test-state'
            }
            
            response = await client.get(callback_url, params=params, follow_redirects=False)
            print(f"Status: {response.status_code}")
            
            if response.status_code == 302:
                redirect_url = response.headers.get('location', '')
                print(f"Redirect URL: {redirect_url}")
                
                if 'callback_failed' in redirect_url:
                    print("✅ Backend correctly handling invalid code")
                else:
                    print("❌ Unexpected redirect")
            else:
                print(f"Response: {response.text}")
                
    except Exception as e:
        print(f"❌ Backend test error: {e}")
    
    print("\n" + "=" * 50)
    print("🏁 Debug Complete")
    print("\n💡 If all tests pass, the issue might be:")
    print("- Google OAuth redirect URI mismatch")
    print("- Network/firewall issues")
    print("- Frontend callback handling")

if __name__ == "__main__":
    asyncio.run(debug_oauth_step_by_step())
