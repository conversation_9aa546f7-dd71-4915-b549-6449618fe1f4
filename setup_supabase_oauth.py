#!/usr/bin/env python3
"""
Setup Supabase table untuk OAuth authentication
"""
import os
from supabase import create_client
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def setup_supabase_oauth():
    """Setup Supabase table for OAuth"""
    
    print("🚀 Setting up Supabase OAuth table...")
    print("=" * 50)
    
    # Get Supabase credentials
    supabase_url = os.getenv("SUPABASE_URL")
    supabase_key = os.getenv("SUPABASE_ANON_KEY")
    
    if not supabase_url or not supabase_key:
        print("❌ Missing Supabase credentials in .env file")
        return False
    
    try:
        # Create Supabase client
        supabase = create_client(supabase_url, supabase_key)
        print("✅ Connected to Supabase")
        
        # Check if user_profiles table exists
        try:
            response = supabase.table('user_profiles').select('count').execute()
            print("✅ user_profiles table already exists")
            
            # Show current structure
            response = supabase.table('user_profiles').select('*').limit(1).execute()
            if response.data:
                print("📋 Current table structure:")
                for key in response.data[0].keys():
                    print(f"   - {key}")
            
        except Exception as e:
            print("❌ user_profiles table does not exist or is not accessible")
            print(f"Error: {e}")
            print("\n💡 Please create the table manually in Supabase Dashboard:")
            print_table_sql()
            return False
        
        # Test insert (will fail if table structure is wrong)
        import uuid
        test_user_id = str(uuid.uuid4())

        test_user = {
            'id': test_user_id,
            'email': '<EMAIL>',
            'full_name': 'Test User',
            'avatar_url': '',
            'provider': 'google',
            'provider_id': 'test-provider-id'
        }
        
        try:
            # Try to insert test user
            response = supabase.table('user_profiles').insert(test_user).execute()
            
            if response.data:
                print("✅ Test user insert successful")
                
                # Clean up test user
                supabase.table('user_profiles').delete().eq('id', test_user_id).execute()
                print("✅ Test user cleaned up")
                
            else:
                print("❌ Test user insert failed")
                return False
                
        except Exception as e:
            print(f"❌ Test user insert failed: {e}")
            print("\n💡 Table structure might be incorrect. Expected columns:")
            print_expected_columns()
            return False
        
        print("\n🎉 Supabase OAuth setup complete!")
        print("✅ user_profiles table is ready for OAuth")
        return True
        
    except Exception as e:
        print(f"❌ Supabase connection failed: {e}")
        return False

def print_table_sql():
    """Print SQL to create user_profiles table"""
    print("\n📝 SQL to create user_profiles table:")
    print("-" * 40)
    print("""
CREATE TABLE user_profiles (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email VARCHAR(255) UNIQUE NOT NULL,
    full_name VARCHAR(255),
    avatar_url TEXT,
    provider VARCHAR(50) DEFAULT 'email',
    provider_id VARCHAR(255),
    is_verified BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create index for faster lookups
CREATE INDEX idx_user_profiles_email ON user_profiles(email);
CREATE INDEX idx_user_profiles_provider ON user_profiles(provider, provider_id);

-- Enable Row Level Security (RLS)
ALTER TABLE user_profiles ENABLE ROW LEVEL SECURITY;

-- Create policy for users to read their own profile
CREATE POLICY "Users can view own profile" ON user_profiles
    FOR SELECT USING (auth.uid()::text = id::text);

-- Create policy for users to update their own profile
CREATE POLICY "Users can update own profile" ON user_profiles
    FOR UPDATE USING (auth.uid()::text = id::text);
""")

def print_expected_columns():
    """Print expected table columns"""
    print("\n📋 Expected table columns:")
    print("-" * 30)
    columns = [
        "id (UUID, PRIMARY KEY)",
        "email (VARCHAR, UNIQUE, NOT NULL)",
        "full_name (VARCHAR)",
        "avatar_url (TEXT)",
        "provider (VARCHAR, DEFAULT 'email')",
        "provider_id (VARCHAR)",
        "is_verified (BOOLEAN, DEFAULT FALSE)",
        "created_at (TIMESTAMP)",
        "updated_at (TIMESTAMP)"
    ]
    
    for col in columns:
        print(f"   - {col}")

if __name__ == "__main__":
    success = setup_supabase_oauth()
    
    if success:
        print("\n🚀 Ready to test OAuth!")
        print("Run: python test_oauth.py")
    else:
        print("\n🔧 Please fix the issues above before testing OAuth")
