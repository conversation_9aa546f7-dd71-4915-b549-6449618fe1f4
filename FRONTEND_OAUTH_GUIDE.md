# 🔧 Frontend OAuth Integration Guide

## 📋 Masalah yang Ditemukan

Be<PERSON>sarkan log backend, OAuth flow berhasil sampai ke callback, tetapi frontend menampilkan error `callback_failed`. Ini menunjukkan masalah di frontend dalam menangani response dari backend.

## ✅ Backend Sudah Diperbaiki

### Perbaikan yang telah dilakukan:
1. ✅ Method `create_or_update_oauth_user` → `create_or_update_user`
2. ✅ Token generation diperbaiki (`email` field)
3. ✅ Logging ditambahkan untuk debugging
4. ✅ Database diaktifkan (`USE_DATABASE=true`)

### OAuth Flow Backend:
```
GET /api/v1/auth/oauth/google → Redirect ke Google
GET /api/v1/auth/oauth/callback/google → Handle callback
→ Redirect ke http://localhost:3001/auth/callback?access_token=...&user_id=...
```

## 🚨 Yang Perlu Diperbaiki di Frontend

### 1. OAuth Callback Handler

Frontend perlu menangani URL callback dengan parameter:
```
http://localhost:3001/auth/callback?access_token=...&refresh_token=...&user_id=...&email=...&name=...&avatar_url=...&oauth_success=true&provider=google&message=login_successful
```

### 2. React Component untuk OAuth Callback

Buat atau update komponen `AuthCallback.jsx`:

```jsx
// src/components/auth/AuthCallback.jsx
import React, { useEffect, useState } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';

const AuthCallback = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const { login } = useAuth();
  const [status, setStatus] = useState('processing');

  useEffect(() => {
    const handleOAuthCallback = async () => {
      try {
        // Get parameters from URL
        const accessToken = searchParams.get('access_token');
        const refreshToken = searchParams.get('refresh_token');
        const userId = searchParams.get('user_id');
        const email = searchParams.get('email');
        const name = searchParams.get('name');
        const avatarUrl = searchParams.get('avatar_url');
        const oauthSuccess = searchParams.get('oauth_success');
        const provider = searchParams.get('provider');
        const message = searchParams.get('message');

        console.log('OAuth callback received:', {
          oauthSuccess,
          provider,
          message,
          email,
          userId
        });

        if (oauthSuccess === 'true' && accessToken) {
          // Store tokens
          localStorage.setItem('access_token', accessToken);
          localStorage.setItem('refresh_token', refreshToken);
          
          // Update auth context
          await login({
            id: userId,
            email,
            name,
            avatar_url: avatarUrl,
            provider
          });

          setStatus('success');
          
          // Redirect to dashboard
          setTimeout(() => {
            navigate('/dashboard');
          }, 2000);

        } else {
          // Handle error
          const error = searchParams.get('error') || 'OAuth login failed';
          console.error('OAuth error:', error);
          setStatus('error');
          
          setTimeout(() => {
            navigate('/login');
          }, 3000);
        }

      } catch (error) {
        console.error('OAuth callback error:', error);
        setStatus('error');
        setTimeout(() => {
          navigate('/login');
        }, 3000);
      }
    };

    handleOAuthCallback();
  }, [searchParams, navigate, login]);

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="max-w-md w-full space-y-8">
        <div className="text-center">
          {status === 'processing' && (
            <>
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
              <h2 className="mt-6 text-xl font-semibold text-gray-900">
                Processing OAuth login...
              </h2>
            </>
          )}
          
          {status === 'success' && (
            <>
              <div className="text-green-600 text-4xl mb-4">✅</div>
              <h2 className="text-xl font-semibold text-gray-900">
                Login successful!
              </h2>
              <p className="text-gray-600">Redirecting to dashboard...</p>
            </>
          )}
          
          {status === 'error' && (
            <>
              <div className="text-red-600 text-4xl mb-4">❌</div>
              <h2 className="text-xl font-semibold text-gray-900">
                Login failed
              </h2>
              <p className="text-gray-600">Redirecting to login page...</p>
            </>
          )}
        </div>
      </div>
    </div>
  );
};

export default AuthCallback;
```

### 3. Update Router Configuration

Pastikan route `/auth/callback` ada di router:

```jsx
// src/App.jsx atau router config
import AuthCallback from './components/auth/AuthCallback';

// Dalam router configuration
<Route path="/auth/callback" element={<AuthCallback />} />
```

### 4. Update OAuth Button

Pastikan tombol "Continue with Google" mengarah ke backend:

```jsx
// src/components/auth/LoginForm.jsx
const handleGoogleLogin = () => {
  // Redirect ke backend OAuth endpoint
  window.location.href = 'http://localhost:8001/api/v1/auth/oauth/google';
};

// Dalam JSX
<button
  onClick={handleGoogleLogin}
  className="w-full flex justify-center items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
>
  <svg className="w-5 h-5 mr-2" viewBox="0 0 24 24">
    {/* Google icon SVG */}
  </svg>
  Continue with Google
</button>
```

## 🧪 Testing

### 1. Test OAuth Flow:
1. Start backend: `uvicorn app.main:app --host 0.0.0.0 --port 8001 --reload`
2. Start frontend: `npm start` (port 3001)
3. Klik "Continue with Google"
4. Complete OAuth di Google
5. Harus redirect ke `/auth/callback` dengan tokens
6. Kemudian redirect ke `/dashboard`

### 2. Debug Frontend:
- Check browser console untuk errors
- Check Network tab untuk requests
- Check localStorage untuk tokens

## 🔍 Debugging

Jika masih ada masalah:

1. **Check browser console** untuk JavaScript errors
2. **Check Network tab** untuk failed requests
3. **Check backend logs** untuk OAuth callback details
4. **Verify URL parameters** di `/auth/callback`

## 📝 Catatan Penting

- Backend sudah mengirim semua data yang diperlukan via URL parameters
- Frontend hanya perlu mengambil parameters dan menyimpan tokens
- Pastikan CORS settings memungkinkan redirect dari backend ke frontend
- Gunakan `window.location.href` untuk OAuth initiation, bukan fetch/axios
