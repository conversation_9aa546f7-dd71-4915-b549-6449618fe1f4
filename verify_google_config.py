#!/usr/bin/env python3
"""
Verify Google OAuth configuration
"""
import os
import requests
from dotenv import load_dotenv
from urllib.parse import urlencode

load_dotenv()

def verify_google_oauth_config():
    """Verify Google OAuth configuration"""
    
    print("🔍 Google OAuth Configuration Verification")
    print("=" * 50)
    
    # Get configuration
    client_id = os.getenv('GOOGLE_CLIENT_ID')
    client_secret = os.getenv('GOOGLE_CLIENT_SECRET')
    redirect_uri = os.getenv('GOOGLE_REDIRECT_URI')
    
    print(f"📋 Current Configuration:")
    print(f"   Client ID: {client_id}")
    print(f"   Redirect URI: {redirect_uri}")
    print(f"   Client Secret: {'*' * 20}")
    
    # Test 1: Check if client ID is valid format
    print(f"\n1. 🔐 Client ID Validation")
    print("-" * 30)
    
    if client_id and client_id.endswith('.apps.googleusercontent.com'):
        print("✅ Client ID format is correct")
    else:
        print("❌ Client ID format is incorrect")
        print("💡 Should end with '.apps.googleusercontent.com'")
        return False
    
    # Test 2: Generate OAuth URL and check parameters
    print(f"\n2. 🔗 OAuth URL Generation")
    print("-" * 30)
    
    oauth_params = {
        'client_id': client_id,
        'redirect_uri': redirect_uri,
        'scope': 'openid email profile',
        'response_type': 'code',
        'state': 'test-state-123',
        'access_type': 'offline',
        'prompt': 'consent'
    }
    
    oauth_url = f"https://accounts.google.com/o/oauth2/v2/auth?{urlencode(oauth_params)}"
    
    print(f"✅ Generated OAuth URL:")
    print(f"   {oauth_url}")
    
    # Test 3: Check redirect URI format
    print(f"\n3. 🎯 Redirect URI Validation")
    print("-" * 30)
    
    expected_redirect = "http://localhost:8001/api/v1/auth/oauth/callback/google"
    
    if redirect_uri == expected_redirect:
        print("✅ Redirect URI is correct")
    else:
        print("❌ Redirect URI mismatch")
        print(f"   Expected: {expected_redirect}")
        print(f"   Current:  {redirect_uri}")
        return False
    
    # Test 4: Test backend OAuth initiation
    print(f"\n4. 🌐 Backend OAuth Initiation Test")
    print("-" * 30)
    
    try:
        response = requests.get("http://localhost:8001/api/v1/auth/oauth/google", 
                              allow_redirects=False, timeout=10)
        
        if response.status_code == 302:
            actual_redirect = response.headers.get('location', '')
            print("✅ Backend OAuth initiation working")
            
            # Check if redirect URL contains correct parameters
            if client_id in actual_redirect and redirect_uri in actual_redirect:
                print("✅ OAuth URL parameters are correct")
            else:
                print("❌ OAuth URL parameters are incorrect")
                print(f"   Generated URL: {actual_redirect}")
                
        else:
            print(f"❌ Backend OAuth initiation failed: {response.status_code}")
            print(f"   Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Backend test failed: {e}")
        print("💡 Make sure backend is running on port 8001")
        return False
    
    # Test 5: Google OAuth endpoint accessibility
    print(f"\n5. 🌍 Google OAuth Endpoint Test")
    print("-" * 30)
    
    try:
        # Test Google's well-known configuration
        response = requests.get('https://accounts.google.com/.well-known/openid_configuration', 
                              timeout=10)
        
        if response.status_code == 200:
            print("✅ Google OAuth endpoints are accessible")
            
            config = response.json()
            auth_endpoint = config.get('authorization_endpoint')
            token_endpoint = config.get('token_endpoint')
            
            print(f"   Auth endpoint: {auth_endpoint}")
            print(f"   Token endpoint: {token_endpoint}")
            
        else:
            print("❌ Cannot access Google OAuth endpoints")
            return False
            
    except Exception as e:
        print(f"❌ Google endpoint test failed: {e}")
        print("💡 Check internet connection")
        return False
    
    print(f"\n" + "=" * 50)
    print("🎯 Configuration Verification Summary")
    print("=" * 50)
    
    print("✅ All configuration checks passed!")
    print("\n📋 Next Steps:")
    print("1. Verify Google Cloud Console redirect URI:")
    print(f"   {redirect_uri}")
    print("2. Test real OAuth flow:")
    print("   python test_real_oauth.py")
    print("3. Check backend logs for detailed errors")
    
    print(f"\n🔧 Google Cloud Console Configuration:")
    print("   Authorized JavaScript origins:")
    print("   - http://localhost:3001")
    print("   - http://localhost:8001")
    print("   - https://fgpyqyiazgouorgpkavr.supabase.co")
    print("")
    print("   Authorized redirect URIs:")
    print(f"   - {redirect_uri}")
    print("   - http://localhost:3001/auth/callback")
    print("   - https://fgpyqyiazgouorgpkavr.supabase.co/auth/v1/callback")
    
    return True

if __name__ == "__main__":
    verify_google_oauth_config()
