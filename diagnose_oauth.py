#!/usr/bin/env python3
"""
Diagnostic script untuk OAuth backend issues
"""
import os
import asyncio
import asyncpg
from supabase import create_client
from dotenv import load_dotenv
import httpx
import json

# Load environment variables
load_dotenv()

async def diagnose_oauth_backend():
    """Comprehensive OAuth backend diagnosis"""
    
    print("🔍 OAuth Backend Diagnosis")
    print("=" * 50)
    
    # 1. Check Environment Variables
    print("\n1. 📋 Environment Variables Check")
    print("-" * 30)
    
    required_vars = [
        'GOOGLE_CLIENT_ID',
        'GOOGLE_CLIENT_SECRET', 
        'GOOGLE_REDIRECT_URI',
        'SECRET_KEY',
        'SUPABASE_URL',
        'SUPABASE_ANON_KEY',
        'DATABASE_URL'
    ]
    
    missing_vars = []
    for var in required_vars:
        value = os.getenv(var)
        if value:
            if 'SECRET' in var or 'KEY' in var:
                print(f"✅ {var}: {'*' * 20}")
            else:
                print(f"✅ {var}: {value}")
        else:
            print(f"❌ {var}: MISSING")
            missing_vars.append(var)
    
    if missing_vars:
        print(f"\n🚨 Missing environment variables: {', '.join(missing_vars)}")
        return False
    
    # 2. Test Google OAuth Configuration
    print("\n2. 🔐 Google OAuth Configuration")
    print("-" * 30)
    
    client_id = os.getenv('GOOGLE_CLIENT_ID')
    redirect_uri = os.getenv('GOOGLE_REDIRECT_URI')
    
    print(f"Client ID: {client_id}")
    print(f"Redirect URI: {redirect_uri}")
    
    # Test if Google recognizes our client
    try:
        async with httpx.AsyncClient() as client:
            # Test Google's well-known configuration
            response = await client.get('https://accounts.google.com/.well-known/openid_configuration')
            if response.status_code == 200:
                print("✅ Google OAuth endpoints accessible")
            else:
                print("❌ Cannot access Google OAuth endpoints")
    except Exception as e:
        print(f"❌ Google OAuth test failed: {e}")
    
    # 3. Test Database Connection
    print("\n3. 🗄️ Database Connection Test")
    print("-" * 30)
    
    database_url = os.getenv('DATABASE_URL')
    if database_url:
        try:
            # Test PostgreSQL connection
            conn = await asyncpg.connect(database_url)
            print("✅ Database connection successful")
            
            # Check if user_profiles table exists
            try:
                result = await conn.fetch("""
                    SELECT table_name 
                    FROM information_schema.tables 
                    WHERE table_schema = 'public' 
                    AND table_name = 'user_profiles'
                """)
                
                if result:
                    print("✅ user_profiles table exists")
                    
                    # Check table structure
                    columns = await conn.fetch("""
                        SELECT column_name, data_type 
                        FROM information_schema.columns 
                        WHERE table_name = 'user_profiles'
                        ORDER BY ordinal_position
                    """)
                    
                    print("📋 Table structure:")
                    for col in columns:
                        print(f"   - {col['column_name']}: {col['data_type']}")
                        
                else:
                    print("❌ user_profiles table does not exist")
                    print("💡 Run: python setup_oauth_database.py")
                    
            except Exception as e:
                print(f"❌ Error checking table structure: {e}")
            
            await conn.close()
            
        except Exception as e:
            print(f"❌ Database connection failed: {e}")
            print("💡 Check DATABASE_URL and database credentials")
    
    # 4. Test Supabase Connection
    print("\n4. 🚀 Supabase Connection Test")
    print("-" * 30)
    
    supabase_url = os.getenv('SUPABASE_URL')
    supabase_key = os.getenv('SUPABASE_ANON_KEY')
    
    if supabase_url and supabase_key:
        try:
            supabase = create_client(supabase_url, supabase_key)
            
            # Test basic connection
            response = supabase.table('user_profiles').select('count').execute()
            print("✅ Supabase connection successful")
            
        except Exception as e:
            print(f"❌ Supabase connection failed: {e}")
    
    # 5. Test Backend Endpoints
    print("\n5. 🌐 Backend Endpoints Test")
    print("-" * 30)
    
    base_url = "http://localhost:8001"
    
    try:
        async with httpx.AsyncClient() as client:
            # Test health endpoint
            response = await client.get(f"{base_url}/health")
            if response.status_code == 200:
                print("✅ Health endpoint working")
            else:
                print(f"❌ Health endpoint failed: {response.status_code}")
            
            # Test OAuth initiation
            response = await client.get(f"{base_url}/api/v1/auth/oauth/google", follow_redirects=False)
            if response.status_code == 302:
                print("✅ OAuth initiation working")
                redirect_url = response.headers.get('location', '')
                if 'accounts.google.com' in redirect_url:
                    print("✅ Redirecting to Google correctly")
                else:
                    print(f"❌ Wrong redirect URL: {redirect_url}")
            else:
                print(f"❌ OAuth initiation failed: {response.status_code}")
                
    except Exception as e:
        print(f"❌ Backend endpoint test failed: {e}")
        print("💡 Make sure backend is running on port 8001")
    
    # 6. JWT Configuration Test
    print("\n6. 🔑 JWT Configuration Test")
    print("-" * 30)
    
    secret_key = os.getenv('SECRET_KEY')
    algorithm = os.getenv('ALGORITHM', 'HS256')
    
    if secret_key:
        print(f"✅ SECRET_KEY configured (length: {len(secret_key)})")
        print(f"✅ Algorithm: {algorithm}")
        
        # Test JWT creation
        try:
            from jose import jwt
            from datetime import datetime, timedelta
            
            test_payload = {
                "sub": "test-user",
                "email": "<EMAIL>",
                "exp": datetime.utcnow() + timedelta(minutes=30)
            }
            
            token = jwt.encode(test_payload, secret_key, algorithm=algorithm)
            decoded = jwt.decode(token, secret_key, algorithms=[algorithm])
            
            print("✅ JWT creation and verification working")
            
        except Exception as e:
            print(f"❌ JWT test failed: {e}")
    else:
        print("❌ SECRET_KEY not configured")
    
    print("\n" + "=" * 50)
    print("🏁 Diagnosis Complete")
    print("\nIf all tests pass, the issue might be in:")
    print("- Google Cloud Console redirect URI configuration")
    print("- OAuth callback handler logic")
    print("- User creation/update process")

if __name__ == "__main__":
    asyncio.run(diagnose_oauth_backend())
